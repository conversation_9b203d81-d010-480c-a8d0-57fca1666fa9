# 医疗设备质量控制体系轮播组件

## 功能概述

在 `QC-system-section` 区域开发了一个轮播模块，包含三种状态的图表展示，每5秒自动切换。

## 功能特性

### 1. 自动轮播
- 每5秒自动切换到下一个状态
- 支持手动点击指示器切换状态
- 平滑的过渡动画效果

### 2. 进度指示器
- 顶部显示三个进度条指示器
- 当前状态的指示器会显示进度动画
- 点击指示器可手动切换状态

### 3. 三种状态展示

#### 状态1: 临床日常质控
- **上半部分**: 显示4个流程图标（临床、医工、医工、专业机构）
- **下半部分**: 表格形式展示日常质控、月度质控、保养的各项指标
- **数据包括**: 已计划、已完成、初检合格率、接收完成率、复检合格率、整改完成率等

#### 状态2: 医工月度质控&保养
- **上半部分**: 同样的4个流程图标
- **下半部分**: 年度质控工单明细表格
- **数据包括**: 设备名称、所属科室、质控类型、质控日期、检测结果、下次日期等

#### 状态3: 医工设备维修
- **上半部分**: 同样的4个流程图标
- **下半部分**: 左侧显示报修和维修的统计圆环，右侧显示高故障设备的进度条
- **数据包括**: 报修台数、维修台数、各设备的故障进度条

## 技术实现

### 组件结构
```
QCSystemCarousel.jsx - 主轮播组件
├── 进度指示器 (carousel-indicators)
├── 轮播内容 (carousel-content)
│   ├── 上半部分 (carousel-header)
│   │   ├── 图标区域 (header-icons)
│   │   └── 进度数据 (progress-section)
│   └── 下半部分 (carousel-body)
│       ├── 状态1内容 (state-daily)
│       ├── 状态2内容 (state-monthly)
│       └── 状态3内容 (state-repair)
```

### 核心功能
1. **自动轮播逻辑**: 使用 `setInterval` 每5秒切换状态
2. **进度条动画**: 独立的进度条动画，每100ms增加2%
3. **状态管理**: 使用 `useState` 管理当前状态和进度
4. **响应式设计**: 使用 CSS Grid 和 Flexbox 实现响应式布局

### 样式特点
- 深色主题，符合大屏展示需求
- 渐变色进度条和按钮
- 表格和卡片式布局
- 平滑的过渡动画

## 使用方法

### 1. 导入组件
```jsx
import QCSystemCarousel from "./components/QCSystemCarousel.jsx";
```

### 2. 在页面中使用
```jsx
<div className="QC-system-section">
    <h3 className="QC-system-title">医疗设备质量控制体系</h3>
    <QCSystemCarousel />
</div>
```

### 3. 访问页面
访问路径: `/RadQCBoard`

## 数据配置

组件内部包含三种状态的模拟数据：

### 进度数据 (progressData)
```javascript
[
    { label: "已计划", value: 50, color: "#7245D9" },
    { label: "已完成", value: 12, color: "#9670EE" },
    // ... 更多数据
]
```

### 内容数据 (contentData)
根据不同状态显示不同格式的数据：
- 状态1: 表格数据
- 状态2: 工单明细数据
- 状态3: 统计数据和进度数据

## 自定义配置

### 修改轮播间隔
在 `QCSystemCarousel.jsx` 中修改：
```javascript
const interval = setInterval(() => {
    // 修改这里的 5000 (5秒)
}, 5000);
```

### 修改进度条速度
```javascript
setProgress((prev) => {
    if (prev >= 100) return 0;
    return prev + 2; // 修改增长速度
});
```

### 添加新状态
1. 在 `statesData` 数组中添加新的状态配置
2. 在 `carousel-body` 中添加对应的渲染逻辑
3. 更新样式文件中的相关样式

## 文件结构

```
src/views/radiologyQCDashboard/
├── index.jsx                          # 主页面文件
├── index.less                         # 主样式文件
└── components/
    ├── QCSystemCarousel.jsx           # 轮播组件
    ├── DataEchart.jsx                 # 图表组件
    └── Rate.jsx                       # 比率组件
```

## 注意事项

1. 组件依赖 `@/images/export` 中的图标资源
2. 样式使用 Less 预处理器
3. 组件会在页面卸载时自动清理定时器
4. 支持手动点击切换，不会影响自动轮播的节奏

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 后续优化建议

1. 添加数据接口对接
2. 增加加载状态和错误处理
3. 支持更多自定义配置选项
4. 添加键盘导航支持
5. 优化移动端显示效果
