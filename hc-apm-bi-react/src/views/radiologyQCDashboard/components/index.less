.rate-wrap {
    position: relative;

    .rate-r {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 10px;
        height: 10px;
        transform: translate(-7px, 16px);

        &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 10px;
            height: 10px;
            border-radius: 100%;
            background: #878c92;
            z-index: 10;
        }

        .rate-pointer {
            position: absolute;
            top: 5px;
            left: 4px;
            width: 1px;
            height: 40px;
            background: #f9f9fa;
        }
    }


}