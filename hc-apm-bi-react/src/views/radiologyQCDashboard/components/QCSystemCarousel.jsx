import React, { useState, useEffect } from "react";
import { icon_1, icon_2, icon_3, icon_4 } from "@/images/export";

const QCSystemCarousel = () => {
    const [currentState, setCurrentState] = useState(0);
    const [progress, setProgress] = useState(0);

    // 三种状态的数据配置
    const statesData = [
        {
            title: "医疗设备质量控制体系",
            subtitle: "临床 | 日常质控",
            progressData: [
                { label: "已计划", value: 50, color: "#7245D9" },
                { label: "已完成", value: 12, color: "#9670EE" },
                { label: "初检合格率", value: 98, color: "#8FBF9D" },
                { label: "接收完成率", value: 100, color: "#00C2EB" },
                { label: "复检合格率", value: 100, color: "#EDC50C" },
                { label: "接收完成率", value: 100, color: "#B6BECC" },
                { label: "整改完成率", value: 24, color: "#A32940" },
                { label: "整改完成率", value: 100, color: "#878C92" },
            ],
            contentData: [
                { name: "日常质控", value: 50 },
                { name: "月度质控", value: 12 },
                { name: "保养", value: 98 },
            ],
            icons: [icon_1, icon_2, icon_3, icon_4],
        },
        {
            title: "医疗设备质量控制体系",
            subtitle: "医工 | 月度质控&保养",
            progressData: [
                { label: "已计划", value: 50, color: "#7245D9" },
                { label: "已完成", value: 12, color: "#9670EE" },
                { label: "初检合格率", value: 98, color: "#8FBF9D" },
                { label: "接收完成率", value: 100, color: "#00C2EB" },
                { label: "复检合格率", value: 100, color: "#EDC50C" },
                { label: "接收完成率", value: 100, color: "#B6BECC" },
                { label: "整改完成率", value: 24, color: "#A32940" },
                { label: "整改完成率", value: 100, color: "#878C92" },
            ],
            contentData: [
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
                {
                    name: "年度质控",
                    value: 2023,
                    date: "2023年11月20日",
                    status: "合格",
                    nextDate: "2024年11月",
                },
            ],
            icons: [icon_1, icon_2, icon_3, icon_4],
        },
        {
            title: "医疗设备质量控制体系",
            subtitle: "医工 | 设备维修",
            progressData: [
                { label: "已计划", value: 50, color: "#7245D9" },
                { label: "已完成", value: 12, color: "#9670EE" },
                { label: "初检合格率", value: 98, color: "#8FBF9D" },
                { label: "接收完成率", value: 100, color: "#00C2EB" },
                { label: "复检合格率", value: 100, color: "#EDC50C" },
                { label: "接收完成率", value: 100, color: "#B6BECC" },
                { label: "整改完成率", value: 24, color: "#A32940" },
                { label: "整改完成率", value: 100, color: "#878C92" },
            ],
            contentData: [
                { name: "报修", count: 99, unit: "台" },
                { name: "维修", count: 99, unit: "台" },
            ],
            repairData: [
                { name: "急诊1楼各类数字化摄影系统质控检测", progress: 60 },
                { name: "门诊1楼数字化摄影系统质控检测", progress: 45 },
                { name: "放射科1号室数字化摄影系统质控检测", progress: 30 },
                { name: "放射科3号室数字化摄影系统质控检测", progress: 85 },
            ],
            icons: [icon_1, icon_2, icon_3, icon_4],
        },
    ];

    // 自动轮播逻辑
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentState((prev) => (prev + 1) % statesData.length);
            setProgress(0); // 重置进度条
        }, 5000);

        return () => clearInterval(interval);
    }, [statesData.length]);

    // 进度条动画
    useEffect(() => {
        const progressInterval = setInterval(() => {
            setProgress((prev) => {
                if (prev >= 100) {
                    return 0;
                }
                return prev + 2; // 每100ms增加2%，5秒完成
            });
        }, 100);

        return () => clearInterval(progressInterval);
    }, [currentState]);

    const currentData = statesData[currentState];

    return (
        <div className="qc-system-carousel">
            {/* 进度指示器 */}
            <div className="carousel-indicators">
                {statesData.map((_, index) => (
                    <div
                        key={index}
                        className={`indicator ${
                            index === currentState ? "active" : ""
                        }`}
                        onClick={() => setCurrentState(index)}
                    >
                        <div className="indicator-progress">
                            {index === currentState && (
                                <div
                                    className="progress-bar"
                                    style={{ width: `${progress}%` }}
                                />
                            )}
                        </div>
                    </div>
                ))}
            </div>

            {/* 轮播内容 */}
            <div className="carousel-content">
                {/* 上半部分 - 进度展示 */}
                <div className="carousel-header">
                    <div className="header-icons">
                        {currentData.icons.map((icon, index) => (
                            <div key={index} className="icon-item">
                                <div className="icon-circle">
                                    <span className="icon-number">
                                        {index + 1}
                                    </span>
                                </div>
                                <img src={icon} alt={`icon-${index + 1}`} />
                                <div className="icon-labels">
                                    <span className="icon-label">
                                        {index === 0 && "临床"}
                                        {index === 1 && "医工"}
                                        {index === 2 && "医工"}
                                        {index === 3 && "专业机构"}
                                    </span>
                                    <span className="icon-sublabel">
                                        {index === 0 && "日常质控"}
                                        {index === 1 && "月度质控&保养"}
                                        {index === 2 && "设备维修"}
                                        {index === 3 && "医学计量"}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="progress-section">
                        <div className="progress-grid">
                            {currentData.progressData.map((item, index) => (
                                <div key={index} className="progress-item">
                                    <span className="progress-label">
                                        {item.label}
                                    </span>
                                    <span className="progress-value">
                                        {item.value}
                                        {typeof item.value === "number" &&
                                        item.value <= 100
                                            ? "%"
                                            : ""}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* 下半部分 - 内容变化 */}
                <div className="carousel-body">
                    {currentState === 0 && (
                        <div className="state-content state-daily">
                            <div className="content-table">
                                <div className="table-row table-header">
                                    <span>日常质控</span>
                                    <span>已计划</span>
                                    <span>已完成</span>
                                    <span>初检合格率</span>
                                    <span>接收完成率</span>
                                    <span>复检合格率</span>
                                    <span>接收完成率</span>
                                    <span>整改完成率</span>
                                    <span>整改完成率</span>
                                </div>
                                <div className="table-row">
                                    <span>日常质控</span>
                                    <span>50</span>
                                    <span>12</span>
                                    <span>98%</span>
                                    <span>100%</span>
                                    <span>100%</span>
                                    <span>100%</span>
                                    <span>24%</span>
                                    <span>100%</span>
                                </div>
                                <div className="table-row">
                                    <span>月度质控</span>
                                    <span>50</span>
                                    <span>12</span>
                                    <span>98%</span>
                                    <span>100%</span>
                                    <span>100%</span>
                                    <span>100%</span>
                                    <span>24%</span>
                                    <span>100%</span>
                                </div>
                                <div className="table-row">
                                    <span>保养</span>
                                    <span>50</span>
                                    <span>12</span>
                                    <span>98%</span>
                                    <span>100%</span>
                                    <span>100%</span>
                                    <span>100%</span>
                                    <span>24%</span>
                                    <span>100%</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {currentState === 1 && (
                        <div className="state-content state-monthly">
                            <div className="monthly-header">
                                <span className="section-title">
                                    年度质控工单明细
                                </span>
                            </div>
                            <div className="monthly-table">
                                <div className="table-header">
                                    <span>设备名称</span>
                                    <span>所属科室</span>
                                    <span>质控类型</span>
                                    <span>本次质控日期</span>
                                    <span>质控初检结果</span>
                                    <span>下次日期估期时间</span>
                                </div>
                                {currentData.contentData.map((item, index) => (
                                    <div key={index} className="table-row">
                                        <span>CT机</span>
                                        <span>放射科</span>
                                        <span>年度质控</span>
                                        <span>{item.date}</span>
                                        <span className="status-success">
                                            ● {item.status}
                                        </span>
                                        <span>{item.nextDate}</span>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {currentState === 2 && (
                        <div className="state-content state-repair">
                            <div className="repair-stats">
                                {currentData.contentData.map((item, index) => (
                                    <div key={index} className="stat-circle">
                                        <div className="circle-content">
                                            <div className="stat-number">
                                                {item.count}
                                            </div>
                                            <div className="stat-unit">
                                                {item.unit}
                                            </div>
                                        </div>
                                        <div className="stat-label">
                                            {item.name}
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="repair-progress">
                                <div className="progress-header">
                                    <span>高故障设备名</span>
                                </div>
                                <div className="progress-list">
                                    {currentData.repairData.map(
                                        (item, index) => (
                                            <div
                                                key={index}
                                                className="repair-item"
                                            >
                                                <div className="repair-name">
                                                    {item.name}
                                                </div>
                                                <div className="repair-bar">
                                                    <div
                                                        className="repair-progress-fill"
                                                        style={{
                                                            width: `${item.progress}%`,
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        )
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default QCSystemCarousel;
